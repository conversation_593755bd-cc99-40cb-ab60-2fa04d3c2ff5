import React, { useState } from 'react';

const Projects: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: 'متجر إلكتروني متكامل',
      description: 'تطبيق تجارة إلكترونية شامل مع نظام دفع متكامل وإدارة المخزون',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'TypeScript', 'Node.js', 'MongoDB'],
      category: 'web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: true
    },
    {
      id: 2,
      title: 'تطبيق إدارة المهام',
      description: 'تطبيق لإدارة المهام والمشاريع مع واجهة سهلة الاستخدام',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'Redux', 'CSS3'],
      category: 'web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: false
    },
    {
      id: 3,
      title: 'لوحة تحكم تحليلية',
      description: 'لوحة تحكم لعرض البيانات والإحصائيات بشكل تفاعلي',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'D3.js', 'Chart.js'],
      category: 'dashboard',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: true
    },
    {
      id: 4,
      title: 'موقع شركة تقنية',
      description: 'موقع تعريفي لشركة تقنية مع تصميم حديث ومتجاوب',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'Tailwind CSS', 'Framer Motion'],
      category: 'website',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: false
    },
    {
      id: 5,
      title: 'تطبيق الطقس',
      description: 'تطبيق لعرض حالة الطقس مع توقعات لعدة أيام',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'API Integration', 'CSS3'],
      category: 'web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: false
    },
    {
      id: 6,
      title: 'منصة تعليمية',
      description: 'منصة تعليمية تفاعلية مع نظام إدارة الدورات والطلاب',
      image: '/api/placeholder/400/300',
      technologies: ['React', 'Node.js', 'PostgreSQL'],
      category: 'web',
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com',
      featured: true
    }
  ];

  const filters = [
    { id: 'all', label: 'جميع المشاريع' },
    { id: 'web', label: 'تطبيقات ويب' },
    { id: 'dashboard', label: 'لوحات تحكم' },
    { id: 'website', label: 'مواقع إلكترونية' }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  const ProjectCard = ({ project }: { project: typeof projects[0] }) => (
    <div className="card overflow-hidden group">
      {/* Project Image */}
      <div className="relative overflow-hidden">
        <div className="w-full h-48 bg-gradient-to-br from-primary-100 to-purple-100 flex items-center justify-center">
          <div className="text-6xl opacity-50">🚀</div>
        </div>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-4">
            <a
              href={project.liveUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              معاينة مباشرة
            </a>
            <a
              href={project.githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
            >
              الكود المصدري
            </a>
          </div>
        </div>

        {/* Featured Badge */}
        {project.featured && (
          <div className="absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium">
            مميز
          </div>
        )}
      </div>

      {/* Project Info */}
      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {project.title}
        </h3>
        <p className="text-gray-600 mb-4">
          {project.description}
        </p>
        
        {/* Technologies */}
        <div className="flex flex-wrap gap-2">
          {project.technologies.map((tech, index) => (
            <span
              key={index}
              className="bg-primary-100 text-primary-800 text-sm px-3 py-1 rounded-full"
            >
              {tech}
            </span>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <section id="projects" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">أعمالي ومشاريعي</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            مجموعة من المشاريع التي عملت عليها باستخدام أحدث التقنيات
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeFilter === filter.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 mb-6">
            هل تريد رؤية المزيد من أعمالي؟
          </p>
          <a
            href="https://github.com"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary text-lg px-8 py-3"
          >
            زيارة GitHub
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
