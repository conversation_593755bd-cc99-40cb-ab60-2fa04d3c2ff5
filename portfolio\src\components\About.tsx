import React from 'react';

const About: React.FC = () => {
  const stats = [
    { number: '3+', label: 'سنوات خبرة' },
    { number: '50+', label: 'مشروع مكتمل' },
    { number: '30+', label: 'عميل راضي' },
    { number: '5+', label: 'تقنية متقنة' },
  ];

  const experiences = [
    {
      title: 'مطور واجهات أمامية أول',
      company: 'شركة التقنيات المتقدمة',
      period: '2022 - الآن',
      description: 'تطوير تطبيقات ويب معقدة باستخدام React و TypeScript، وقيادة فريق من 3 مطورين.',
    },
    {
      title: 'مطور واجهات أمامية',
      company: 'استوديو الإبداع الرقمي',
      period: '2021 - 2022',
      description: 'تطوير مواقع تجارية وتطبيقات تفاعلية، والتعاون مع فرق التصميم والباك إند.',
    },
    {
      title: 'مطور ويب مبتدئ',
      company: 'شركة الحلول التقنية',
      period: '2020 - 2021',
      description: 'بداية المسيرة المهنية في تطوير الويب، تعلم أساسيات React و JavaScript.',
    },
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">نبذة عني</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            مطور واجهات أمامية شغوف بإنشاء تجارب رقمية استثنائية
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* About Text */}
          <div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-6">
              مرحباً، أنا أحمد محمد
            </h3>
            <div className="space-y-4 text-gray-600">
              <p>
                أنا مطور واجهات أمامية متخصص في إنشاء تطبيقات ويب حديثة وتفاعلية. 
                بدأت رحلتي في عالم البرمجة منذ أكثر من 3 سنوات، وخلال هذه الفترة 
                تمكنت من إتقان العديد من التقنيات الحديثة.
              </p>
              <p>
                أؤمن بأن التصميم الجيد والكود النظيف هما أساس أي مشروع ناجح. 
                أحب التحديات التقنية وأسعى دائماً لتعلم أحدث التقنيات والأدوات 
                في مجال تطوير الويب.
              </p>
              <p>
                عندما لا أكون أمام الكمبيوتر، أستمتع بقراءة الكتب التقنية، 
                ممارسة الرياضة، والسفر لاستكشاف ثقافات جديدة.
              </p>
            </div>

            {/* Download CV Button */}
            <div className="mt-8">
              <button className="btn-primary text-lg px-8 py-3">
                تحميل السيرة الذاتية
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center p-6 card"
              >
                <div className="text-4xl font-bold gradient-text mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Experience Timeline */}
        <div>
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            الخبرة المهنية
          </h3>
          <div className="max-w-4xl mx-auto">
            {experiences.map((exp, index) => (
              <div key={index} className="relative">
                {/* Timeline Line */}
                {index !== experiences.length - 1 && (
                  <div className="absolute right-4 top-12 w-0.5 h-24 bg-gray-300"></div>
                )}
                
                {/* Timeline Dot */}
                <div className="absolute right-2 top-6 w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg"></div>
                
                {/* Content */}
                <div className="mr-12 pb-12">
                  <div className="card p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                      <h4 className="text-xl font-semibold text-gray-900">
                        {exp.title}
                      </h4>
                      <span className="text-primary-600 font-medium">
                        {exp.period}
                      </span>
                    </div>
                    <h5 className="text-lg text-gray-700 mb-3">
                      {exp.company}
                    </h5>
                    <p className="text-gray-600">
                      {exp.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
