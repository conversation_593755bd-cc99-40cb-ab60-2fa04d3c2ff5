import React, { useState, useEffect } from 'react';

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMobileMenuOpen(false);
    }
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-sm shadow-lg'
          : 'bg-transparent'
      }`}
    >
      <nav className="container-custom">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <div className="text-2xl font-bold gradient-text">
            أحمد محمد
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => scrollToSection('home')}
              className="text-gray-700 hover:text-primary-600 transition-colors duration-200"
            >
              الرئيسية
            </button>
            <button
              onClick={() => scrollToSection('about')}
              className="text-gray-700 hover:text-primary-600 transition-colors duration-200"
            >
              نبذة عني
            </button>
            <button
              onClick={() => scrollToSection('skills')}
              className="text-gray-700 hover:text-primary-600 transition-colors duration-200"
            >
              المهارات
            </button>
            <button
              onClick={() => scrollToSection('projects')}
              className="text-gray-700 hover:text-primary-600 transition-colors duration-200"
            >
              المشاريع
            </button>
            <button
              onClick={() => scrollToSection('contact')}
              className="btn-primary"
            >
              تواصل معي
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span
                className={`bg-gray-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${
                  isMobileMenuOpen ? 'rotate-45 translate-y-1' : '-translate-y-0.5'
                }`}
              ></span>
              <span
                className={`bg-gray-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5 ${
                  isMobileMenuOpen ? 'opacity-0' : 'opacity-100'
                }`}
              ></span>
              <span
                className={`bg-gray-700 block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm ${
                  isMobileMenuOpen ? '-rotate-45 -translate-y-1' : 'translate-y-0.5'
                }`}
              ></span>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="py-4 space-y-4">
              <button
                onClick={() => scrollToSection('home')}
                className="block w-full text-right px-4 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200"
              >
                الرئيسية
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="block w-full text-right px-4 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200"
              >
                نبذة عني
              </button>
              <button
                onClick={() => scrollToSection('skills')}
                className="block w-full text-right px-4 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200"
              >
                المهارات
              </button>
              <button
                onClick={() => scrollToSection('projects')}
                className="block w-full text-right px-4 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-200"
              >
                المشاريع
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="block w-full text-right px-4 py-2 btn-primary mx-4"
              >
                تواصل معي
              </button>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
