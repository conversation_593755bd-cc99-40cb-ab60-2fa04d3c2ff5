import React from 'react';

const Skills: React.FC = () => {
  const skillCategories = [
    {
      title: 'Frontend Development',
      skills: [
        { name: 'React', level: 95, icon: '⚛️' },
        { name: 'TypeScript', level: 90, icon: '📘' },
        { name: 'JavaScript', level: 95, icon: '🟨' },
        { name: 'HTML5', level: 98, icon: '🌐' },
        { name: 'CSS3', level: 92, icon: '🎨' },
        { name: 'Tailwind CSS', level: 88, icon: '💨' },
      ]
    },
    {
      title: 'Tools & Technologies',
      skills: [
        { name: 'Git', level: 85, icon: '📚' },
        { name: 'Webpack', level: 75, icon: '📦' },
        { name: 'Vite', level: 80, icon: '⚡' },
        { name: 'npm/yarn', level: 90, icon: '📋' },
        { name: 'VS Code', level: 95, icon: '💻' },
        { name: 'Figma', level: 70, icon: '🎯' },
      ]
    },
    {
      title: 'Backend & Database',
      skills: [
        { name: 'Node.js', level: 75, icon: '🟢' },
        { name: 'Express.js', level: 70, icon: '🚀' },
        { name: 'MongoDB', level: 65, icon: '🍃' },
        { name: 'PostgreSQL', level: 60, icon: '🐘' },
        { name: 'REST APIs', level: 80, icon: '🔗' },
        { name: 'GraphQL', level: 55, icon: '📊' },
      ]
    }
  ];

  const certifications = [
    {
      title: 'React Developer Certification',
      issuer: 'Meta',
      year: '2023',
      icon: '🏆'
    },
    {
      title: 'JavaScript Algorithms and Data Structures',
      issuer: 'freeCodeCamp',
      year: '2022',
      icon: '📜'
    },
    {
      title: 'Frontend Web Development',
      issuer: 'Coursera',
      year: '2021',
      icon: '🎓'
    }
  ];

  return (
    <section id="skills" className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">المهارات والخبرات</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            التقنيات والأدوات التي أتقنها في تطوير تطبيقات الويب الحديثة
          </p>
        </div>

        {/* Skills Categories */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {skillCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="card p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
                {category.title}
              </h3>
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{skill.icon}</span>
                        <span className="font-medium text-gray-900">{skill.name}</span>
                      </div>
                      <span className="text-sm text-gray-600">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${skill.level}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Certifications */}
        <div>
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            الشهادات والإنجازات
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {certifications.map((cert, index) => (
              <div key={index} className="card p-6 text-center">
                <div className="text-4xl mb-4">{cert.icon}</div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {cert.title}
                </h4>
                <p className="text-gray-600 mb-2">{cert.issuer}</p>
                <span className="inline-block bg-primary-100 text-primary-800 text-sm px-3 py-1 rounded-full">
                  {cert.year}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Skills */}
        <div className="mt-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            مهارات إضافية
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Responsive Design', icon: '📱' },
              { name: 'Performance Optimization', icon: '⚡' },
              { name: 'SEO', icon: '🔍' },
              { name: 'Accessibility', icon: '♿' },
              { name: 'Testing', icon: '🧪' },
              { name: 'Agile/Scrum', icon: '🔄' },
              { name: 'UI/UX Design', icon: '🎨' },
              { name: 'Problem Solving', icon: '🧩' },
            ].map((skill, index) => (
              <div key={index} className="card p-4 text-center hover:scale-105 transition-transform duration-200">
                <div className="text-3xl mb-2">{skill.icon}</div>
                <h4 className="font-medium text-gray-900">{skill.name}</h4>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
