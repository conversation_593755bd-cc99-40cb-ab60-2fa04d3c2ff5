@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap');

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #ffffff;
  color: #111827;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  border: none;
  background: none;
  font-family: inherit;
  cursor: pointer;
}

/* Remove default link styles */
a {
  text-decoration: none;
  color: inherit;
}

/* ===== COMPONENT STYLES ===== */

/* Buttons */
.btn-primary {
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #0f172a;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
  transform: translateY(-1px);
}

/* Layout */
.section-padding {
  padding: 64px 16px;
}

@media (min-width: 640px) {
  .section-padding {
    padding: 64px 24px;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 64px 32px;
  }
}

.container-custom {
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* Text Effects */
.gradient-text {
  background: linear-gradient(135deg, #2563eb 0%, #9333ea 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: 700;
}

/* ===== ANIMATIONS ===== */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* ===== UTILITY CLASSES ===== */

/* Display */
.flex { display: flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Flex & Grid */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Grid Columns */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Spacing */
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }
.gap-16 { gap: 4rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Text Alignment */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

/* Font Sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

/* Font Weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Colors */
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-primary-600 { color: #2563eb; }
.text-primary-800 { color: #1e40af; }
.text-yellow-900 { color: #78350f; }

/* Background Colors */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }
.bg-primary-100 { background-color: #dbeafe; }
.bg-primary-600 { background-color: #2563eb; }
.bg-primary-800 { background-color: #1e40af; }
.bg-yellow-400 { background-color: #fbbf24; }
.bg-green-400 { background-color: #34d399; }
.bg-pink-400 { background-color: #f472b6; }

/* Gradients */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-from), var(--tw-gradient-to));
}
.from-blue-50 { --tw-gradient-from: #eff6ff; }
.to-indigo-100 { --tw-gradient-to: #e0e7ff; }
.from-primary-100 { --tw-gradient-from: #dbeafe; }
.to-purple-100 { --tw-gradient-to: #f3e8ff; }
.from-primary-400 { --tw-gradient-from: #60a5fa; }
.to-purple-600 { --tw-gradient-to: #9333ea; }

/* Padding */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }

/* Margin */
.m-0 { margin: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.mt-16 { margin-top: 4rem; }
.mt-20 { margin-top: 5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mr-12 { margin-right: 3rem; }

/* Width & Height */
.w-0\.5 { width: 0.125rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-48 { width: 12rem; }
.w-80 { width: 20rem; }
.w-full { width: 100%; }

.h-0\.5 { height: 0.125rem; }
.h-2 { height: 0.5rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-24 { height: 6rem; }
.h-48 { height: 12rem; }
.h-80 { height: 20rem; }

.min-h-screen { min-height: 100vh; }

.max-w-md { max-width: 28rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-7xl { max-width: 80rem; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* Borders */
.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-4 { border-width: 4px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-800 { border-color: #1f2937; }
.border-white { border-color: #ffffff; }

/* Shadows */
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.top-0 { top: 0; }
.top-4 { top: 1rem; }
.top-6 { top: 1.5rem; }
.top-12 { top: 3rem; }
.top-1\/2 { top: 50%; }
.right-0 { right: 0; }
.right-2 { right: 0.5rem; }
.right-4 { right: 1rem; }
.left-0 { left: 0; }
.left-4 { left: 1rem; }
.bottom-4 { bottom: 1rem; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-50 { z-index: 50; }

/* Overflow */
.overflow-hidden { overflow: hidden; }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

/* Transitions */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out; }
.transition-transform { transition: transform 0.15s ease-in-out; }
.transition-opacity { transition: opacity 0.15s ease-in-out; }

.duration-200 { transition-duration: 0.2s; }
.duration-300 { transition-duration: 0.3s; }
.duration-1000 { transition-duration: 1s; }

/* Cursor */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* Resize */
.resize-none { resize: none; }

/* Backdrop */
.backdrop-blur-sm { backdrop-filter: blur(4px); }

/* Hover States */
.hover\:bg-primary-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:text-white:hover { color: #ffffff; }
.hover\:text-primary-600:hover { color: #2563eb; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Focus States */
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.5); }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }

/* Group Hover */
.group:hover .group-hover\:bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }
.group:hover .group-hover\:opacity-100 { opacity: 1; }

/* Background Opacity */
.bg-opacity-0 { background-color: rgba(0, 0, 0, 0); }
.bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }

/* Background with opacity for white */
.bg-white\/95 { background-color: rgba(255, 255, 255, 0.95); }

/* Transform */
.translate-y-0\.5 { transform: translateY(0.125rem); }
.translate-y-1 { transform: translateY(0.25rem); }
.-translate-y-0\.5 { transform: translateY(-0.125rem); }
.-translate-y-1 { transform: translateY(-0.25rem); }
.rotate-45 { transform: rotate(45deg); }
.-rotate-45 { transform: rotate(-45deg); }

/* ===== RESPONSIVE DESIGN ===== */

/* Small screens (640px and up) */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:flex-row { flex-direction: row; }
}

/* Medium screens (768px and up) */
@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:block { display: block; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:items-center { align-items: center; }
  .md\:justify-between { justify-content: space-between; }
  .md\:mt-0 { margin-top: 0; }
  .md\:text-right { text-align: right; }
}

/* Large screens (1024px and up) */
@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:text-6xl { font-size: 3.75rem; line-height: 1; }
  .lg\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .lg\:text-right { text-align: right; }
  .lg\:text-left { text-align: left; }
  .lg\:justify-start { justify-content: flex-start; }
  .lg\:justify-end { justify-content: flex-end; }
  .lg\:mx-0 { margin-left: 0; margin-right: 0; }
}

/* ===== FORM STYLES ===== */
input[type="text"],
input[type="email"],
textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  font-family: inherit;
}

input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* ===== SPECIAL UTILITY CLASSES ===== */
.pb-12 { padding-bottom: 3rem; }
.rows-6 { rows: 6; }

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }
.gap-16 { gap: 4rem; }

.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-4 { margin-top: 1rem; }
.mt-8 { margin-top: 2rem; }
.mt-16 { margin-top: 4rem; }
.mt-20 { margin-top: 5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-48 { width: 12rem; }
.w-80 { width: 20rem; }
.w-full { width: 100%; }

.h-2 { height: 0.5rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-24 { height: 6rem; }
.h-48 { height: 12rem; }
.h-80 { height: 20rem; }

.min-h-screen { min-height: 100vh; }

.max-w-md { max-width: 28rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-7xl { max-width: 80rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-6 { line-height: 1.5rem; }

.text-white { color: white; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }

.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

.bg-primary-100 { background-color: #dbeafe; }
.bg-primary-600 { background-color: #2563eb; }
.bg-primary-800 { background-color: #1e40af; }

.bg-yellow-400 { background-color: #fbbf24; }
.bg-green-400 { background-color: #34d399; }
.bg-pink-400 { background-color: #f472b6; }

.text-primary-600 { color: #2563eb; }
.text-primary-800 { color: #1e40af; }
.text-yellow-900 { color: #78350f; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.border { border-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-800 { border-color: #1f2937; }

.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.transition-shadow { transition: box-shadow 0.15s ease-in-out; }
.transition-transform { transition: transform 0.15s ease-in-out; }

.duration-200 { transition-duration: 0.2s; }
.duration-300 { transition-duration: 0.3s; }

.hover\:bg-primary-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:text-white:hover { color: white; }
.hover\:text-primary-600:hover { color: #2563eb; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover\:scale-105:hover { transform: scale(1.05); }

.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.resize-none { resize: none; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.top-0 { top: 0; }
.top-4 { top: 1rem; }
.top-6 { top: 1.5rem; }
.top-12 { top: 3rem; }
.top-1\/2 { top: 50%; }
.right-0 { right: 0; }
.right-2 { right: 0.5rem; }
.right-4 { right: 1rem; }
.left-0 { left: 0; }
.left-4 { left: 1rem; }
.bottom-4 { bottom: 1rem; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-50 { z-index: 50; }

.overflow-hidden { overflow: hidden; }

.group:hover .group-hover\:bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }
.group:hover .group-hover\:opacity-100 { opacity: 1; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.bg-opacity-0 { background-color: rgba(0, 0, 0, 0); }
.bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }

.backdrop-blur-sm { backdrop-filter: blur(4px); }

.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-50 { --tw-gradient-from: #eff6ff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0)); }
.to-indigo-100 { --tw-gradient-to: #e0e7ff; }
.from-primary-100 { --tw-gradient-from: #dbeafe; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(219, 234, 254, 0)); }
.to-purple-100 { --tw-gradient-to: #f3e8ff; }
.from-primary-400 { --tw-gradient-from: #60a5fa; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0)); }
.to-purple-600 { --tw-gradient-to: #9333ea; }

/* Responsive Design */
@media (min-width: 640px) {
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:flex-row { flex-direction: row; }
}

@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:items-center { align-items: center; }
  .md\:justify-between { justify-content: space-between; }
  .md\:mt-0 { margin-top: 0; }
}

@media (min-width: 1024px) {
  .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:text-6xl { font-size: 3.75rem; }
  .lg\:text-3xl { font-size: 1.875rem; }
  .lg\:text-right { text-align: right; }
  .lg\:text-left { text-align: left; }
  .lg\:justify-start { justify-content: flex-start; }
  .lg\:justify-end { justify-content: flex-end; }
  .lg\:mx-0 { margin-left: 0; margin-right: 0; }
}
